import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import '../../models/order_item.dart';
import '../../services/pdf_export_service.dart';
import '../../services/pdf_font_service.dart';

class CuttingOptimizationScreen extends StatefulWidget {
  final OrderItem orderItem;
  final List<Map<String, dynamic>> measurements;
  final double stickLength;
  final double sawBladeThickness;

  const CuttingOptimizationScreen({
    super.key,
    required this.orderItem,
    required this.measurements,
    required this.stickLength,
    required this.sawBladeThickness,
  });

  @override
  State<CuttingOptimizationScreen> createState() => _CuttingOptimizationScreenState();
}

class _CuttingOptimizationScreenState extends State<CuttingOptimizationScreen> {
  List<OptimizedStick> _optimizedSticks = [];
  bool _isCalculating = false;
  double _totalWaste = 0.0;
  double _wastePercentage = 0.0;
  final GlobalKey _repaintBoundaryKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _calculateOptimization();
  }

  Future<void> _calculateOptimization() async {
    setState(() {
      _isCalculating = true;
    });

    // Simulate calculation delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));

    final optimizedSticks = _optimizeCutting();

    setState(() {
      _optimizedSticks = optimizedSticks;
      _isCalculating = false;
      _calculateWasteStatistics();
    });
  }

  List<OptimizedStick> _optimizeCutting() {
    final List<OptimizedStick> sticks = [];
    final List<CuttingPiece> remainingPieces = [];

    // Convert measurements to cutting pieces
    for (final measurement in widget.measurements) {
      final pieceSize = _parsePieceSize(measurement['piece_size'] ?? '');
      if (pieceSize > 0) {
        for (int i = 0; i < (measurement['quantity'] ?? 0); i++) {
          remainingPieces.add(CuttingPiece(
            size: pieceSize,
            type: measurement['type'] ?? '-',
            number: measurement['number'] ?? '-',
            originalSpec: measurement['piece_size'] ?? '',
          ));
        }
      }
    }

    // Sort pieces by size (largest first) for better optimization
    remainingPieces.sort((a, b) => b.size.compareTo(a.size));

    int stickNumber = 1;
    while (remainingPieces.isNotEmpty) {
      final stick = OptimizedStick(
        stickNumber: stickNumber,
        totalLength: widget.stickLength,
        sawBladeThickness: widget.sawBladeThickness,
      );

      // Try to fit pieces into this stick
      double currentPosition = 0.0;
      final List<CuttingPiece> piecesToRemove = [];

      for (final piece in remainingPieces) {
        final requiredLength = piece.size + (stick.pieces.isNotEmpty ? widget.sawBladeThickness : 0);

        if (currentPosition + requiredLength <= widget.stickLength) {
          stick.pieces.add(piece.copyWith(position: currentPosition));
          currentPosition += requiredLength;
          piecesToRemove.add(piece);
        }
      }

      // Remove fitted pieces from remaining list
      for (final piece in piecesToRemove) {
        remainingPieces.remove(piece);
      }

      stick.wasteLength = widget.stickLength - currentPosition;
      sticks.add(stick);
      stickNumber++;
    }

    return sticks;
  }

  double _parsePieceSize(String sizeStr) {
    // Extract numeric value from size string (e.g., "25 سم" -> 25.0)
    final RegExp regex = RegExp(r'(\d+(?:\.\d+)?)');
    final match = regex.firstMatch(sizeStr);
    if (match != null) {
      return double.tryParse(match.group(1) ?? '') ?? 0.0;
    }
    return 0.0;
  }

  void _calculateWasteStatistics() {
    _totalWaste = _optimizedSticks.fold(0.0, (sum, stick) => sum + stick.wasteLength);
    final totalMaterial = _optimizedSticks.length * widget.stickLength;
    _wastePercentage = totalMaterial > 0 ? (_totalWaste / totalMaterial) * 100 : 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('عملية التقطيع - ${widget.orderItem.itemName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          if (!_isCalculating)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _calculateOptimization,
              tooltip: 'إعادة حساب التقطيع',
            ),
          if (!_isCalculating && _optimizedSticks.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.picture_as_pdf),
              onPressed: _exportToPdf,
              tooltip: 'حفظ كـ PDF',
            ),
          if (!_isCalculating && _optimizedSticks.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.image),
              onPressed: _exportAsImageOnly,
              tooltip: 'حفظ كصورة',
            ),
          if (!_isCalculating && _optimizedSticks.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareResults,
              tooltip: 'مشاركة النتائج',
            ),
        ],
      ),
      body: _isCalculating
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري حساب التقطيع الأمثل...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _calculateOptimization,
              child: ScrollConfiguration(
                behavior: ScrollConfiguration.of(context).copyWith(
                  scrollbars: true,
                ),
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 12.0 : 16.0),
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: RepaintBoundary(
                    key: _repaintBoundaryKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSummaryCard(),
                        const SizedBox(height: 24),
                        _buildOptimizationResults(),
                        // Add some bottom padding for better mobile experience
                        SizedBox(height: MediaQuery.of(context).size.width < 600 ? 80 : 24),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  // Add PDF export functionality
  Future<void> _exportToPdf() async {
    try {
      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(width: 16),
                Text('جاري إنشاء ملف PDF...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Force reload fonts to ensure proper display
      await PdfFontService.forceReloadFonts();

      final pdf = pw.Document(
        theme: PdfFontService.createTheme(),
      );

      // Add first page with header and statistics
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Container(
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'تقرير التقطيع الأمثل',
                        style: PdfFontService.headerStyle(
                          fontSize: 24,
                          color: PdfColors.blue800,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'العنصر: ${widget.orderItem.itemName}',
                        style: PdfFontService.bodyStyle(fontSize: 16),
                      ),
                      pw.Text(
                        'طول العود: ${PdfFontService.formatNumber(widget.stickLength)} سم',
                        style: PdfFontService.bodyStyle(fontSize: 14),
                      ),
                      pw.Text(
                        'سمك المنشار: ${PdfFontService.formatNumber(widget.sawBladeThickness)} سم',
                        style: PdfFontService.bodyStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Statistics
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text(
                            'عدد الأعواد',
                            style: PdfFontService.tableHeaderStyle(),
                          ),
                          pw.Text(
                            '${_optimizedSticks.length}',
                            style: PdfFontService.tableDataStyle(),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'إجمالي الهدر',
                            style: PdfFontService.tableHeaderStyle(),
                          ),
                          pw.Text(
                            '${PdfFontService.formatNumber(_totalWaste)} سم',
                            style: PdfFontService.tableDataStyle(),
                          ),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text(
                            'نسبة الهدر',
                            style: PdfFontService.tableHeaderStyle(),
                          ),
                          pw.Text(
                            PdfFontService.formatPercentage(_wastePercentage),
                            style: PdfFontService.numberStyle(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 30),

                // Sticks details header
                pw.Text(
                  'تفاصيل الأعواد:',
                  style: PdfFontService.headerStyle(
                    fontSize: 18,
                    color: PdfColors.black,
                  ),
                ),

                pw.SizedBox(height: 15),

                // Add first 3 sticks in the first page to utilize space
                ...List.generate(
                  _optimizedSticks.length > 3 ? 3 : _optimizedSticks.length,
                  (index) => pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 6),
                    child: _buildCompactStickWidget(_optimizedSticks[index]),
                  ),
                ),
              ],
            );
          },
        ),
      );

      // Add remaining sticks on separate pages (5-6 sticks per page with compact design)
      if (_optimizedSticks.length > 3) {
        final remainingSticks = _optimizedSticks.skip(3).toList();

        // Group sticks into pages (5-6 sticks per page with compact design)
        for (int i = 0; i < remainingSticks.length; i += 5) {
          final sticksForPage = remainingSticks.skip(i).take(5).toList();

          pdf.addPage(
            pw.Page(
              pageFormat: PdfPageFormat.a4,
              textDirection: pw.TextDirection.rtl,
              build: (pw.Context context) {
                return pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Page header
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.grey100,
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'تفاصيل الأعواد (تتمة)',
                            style: PdfFontService.headerStyle(
                              fontSize: 16,
                              color: PdfColors.black,
                            ),
                          ),
                          pw.Text(
                            'صفحة ${(i ~/ 5) + 2}',
                            style: PdfFontService.bodyStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),

                    pw.SizedBox(height: 20),

                    // Add sticks for this page with minimal spacing
                    ...sticksForPage.map((stick) => pw.Container(
                      margin: const pw.EdgeInsets.only(bottom: 8),
                      child: _buildCompactStickWidget(stick),
                    )),
                  ],
                );
              },
            ),
          );
        }
      }

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      // Get file name
      final fileName = PdfExportService.getDefaultFileName(
        'تقطيع_${widget.orderItem.itemName}',
      );

      // Save file using the improved service
      if (mounted) {
        await PdfExportService.savePdfFile(
          context: context,
          pdfBytes: pdfBytes,
          fileName: fileName,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Export as image only (direct image export)
  Future<void> _exportAsImageOnly() async {
    try {
      // Debug logging for image export
      debugPrint('\n=== بدء تصدير الصورة ===');
      debugPrint('إجمالي الأعواد: ${_optimizedSticks.length}');

      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(width: 16),
                Text('جاري إنشاء الصورة...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      await _createAndSaveImage();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الصورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير الصورة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Create and save as image
  Future<void> _createAndSaveImage() async {
    try {
      // Capture the widget as image
      RenderRepaintBoundary boundary = _repaintBoundaryKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // Save image file
      debugPrint('تم إنشاء صورة بحجم ${pngBytes.length} بايت');

      // Get file name
      final fileName = PdfExportService.getDefaultFileName(
        'تقطيع_${widget.orderItem.itemName}',
      ).replaceAll('.pdf', '.png');

      // Save image using the export service
      if (mounted) {
        await PdfExportService.saveImageFile(
          context: context,
          imageBytes: pngBytes,
          fileName: fileName,
        );
      }

      debugPrint('تم حفظ الصورة: $fileName');

    } catch (e) {
      debugPrint('خطأ في إنشاء الصورة: $e');
      rethrow;
    }
  }

  // Build visual stick representation (matches app design)
  pw.Widget _buildStickVisualization(OptimizedStick stick) {
    final totalLength = widget.stickLength;
    final usedLength = stick.pieces.fold(0.0, (sum, piece) => sum + piece.size);
    final wasteLength = totalLength - usedLength;

    // Colors for different pieces
    final pieceColors = [
      PdfColors.green300,
      PdfColors.blue300,
      PdfColors.orange300,
      PdfColors.purple300,
      PdfColors.teal300,
      PdfColors.pink300,
      PdfColors.yellow300,
      PdfColors.cyan300,
    ];

    return pw.Container(
      height: 40,
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Row(
        children: [
          // Pieces
          ...stick.pieces.asMap().entries.map((entry) {
            final index = entry.key;
            final piece = entry.value;
            final widthRatio = piece.size / totalLength;
            final color = pieceColors[index % pieceColors.length];

            return pw.Expanded(
              flex: (widthRatio * 1000).round(),
              child: pw.Container(
                height: 40,
                decoration: pw.BoxDecoration(
                  color: color,
                  border: pw.Border(
                    right: pw.BorderSide(
                      color: PdfColors.white,
                      width: 1,
                    ),
                  ),
                ),
                child: pw.Center(
                  child: pw.Text(
                    PdfFontService.formatNumber(piece.size),
                    style: PdfFontService.tableDataStyle(
                      fontSize: 12, // تكبير الخط من 8 إلى 12
                      color: PdfColors.black,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
            );
          }),

          // Waste section (if any)
          if (wasteLength > 0)
            pw.Expanded(
              flex: ((wasteLength / totalLength) * 1000).round(),
              child: pw.Container(
                height: 40,
                decoration: pw.BoxDecoration(
                  color: PdfColors.red200,
                ),
                child: pw.Center(
                  child: pw.Text(
                    PdfFontService.formatNumber(wasteLength),
                    style: PdfFontService.tableDataStyle(
                      fontSize: 8,
                      color: PdfColors.red800,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build compact stick widget for PDF (matches app design)
  pw.Widget _buildCompactStickWidget(OptimizedStick stick) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 6),
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
        color: PdfColors.white,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Header with all info in one row (saves space)
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // Left side: Stick number
                pw.Text(
                  'العود رقم ${stick.stickNumber}',
                  style: PdfFontService.headerStyle(
                    fontSize: 14,
                    color: PdfColors.blue800,
                  ),
                ),
                // Right side: All other info
                pw.Row(
                  children: [
                    pw.Text(
                      'قطع: ${stick.pieces.length}',
                      style: PdfFontService.bodyStyle(fontSize: 11),
                    ),
                    pw.SizedBox(width: 8),
                    pw.Text(
                      'كفاءة: ${PdfFontService.formatPercentage((widget.stickLength - stick.wasteLength) / widget.stickLength * 100)}',
                      style: PdfFontService.numberStyle(
                        fontSize: 11,
                        color: PdfColors.green700,
                      ),
                    ),
                    pw.SizedBox(width: 8),
                    pw.Text(
                      'هدر: ${PdfFontService.formatNumber(stick.wasteLength)} سم',
                      style: PdfFontService.bodyStyle(
                        fontSize: 11,
                        color: PdfColors.red700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 6),

          // Pieces label
          pw.Text(
            'القطع:',
            style: PdfFontService.tableHeaderStyle(fontSize: 11),
          ),

          pw.SizedBox(height: 4),

          // Visual stick representation (matches app design)
          _buildStickVisualization(stick),
        ],
      ),
    );
  }

  // Add share functionality
  Future<void> _shareResults() async {
    // This could be implemented to share cutting results
    // For now, we'll show a snackbar
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('سيتم إضافة ميزة المشاركة قريباً'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }

  Widget _buildSummaryCard() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              Colors.white,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(MediaQuery.of(context).size.width < 600 ? 12.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.analytics,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ملخص التقطيع الأمثل',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'نتائج محسوبة تلقائياً لتقليل الهدر',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Enhanced Statistics with better responsive design
              LayoutBuilder(
                builder: (context, constraints) {
                  bool isMobile = constraints.maxWidth < 600;
                  bool isTablet = constraints.maxWidth >= 600 && constraints.maxWidth < 900;

                  if (isMobile) {
                    // Mobile: 2x2 grid
                    return GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      childAspectRatio: 1.8,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      children: [
                        _buildEnhancedStatCard(
                          'عدد الأعواد',
                          '${_optimizedSticks.length}',
                          Icons.straighten,
                          Colors.blue,
                          'عود',
                          isCompact: true,
                        ),
                        _buildEnhancedStatCard(
                          'طول العود',
                          widget.stickLength.toStringAsFixed(0),
                          Icons.height,
                          Colors.green,
                          'سم',
                          isCompact: true,
                        ),
                        _buildEnhancedStatCard(
                          'إجمالي الهدر',
                          _totalWaste.toStringAsFixed(1),
                          Icons.delete_outline,
                          Colors.orange,
                          'سم',
                          isCompact: true,
                        ),
                        _buildEnhancedStatCard(
                          'نسبة الهدر',
                          _wastePercentage.toStringAsFixed(1),
                          Icons.pie_chart,
                          _wastePercentage < 10 ? Colors.green : _wastePercentage < 20 ? Colors.orange : Colors.red,
                          '%',
                          isCompact: true,
                        ),
                      ],
                    );
                  } else if (isTablet) {
                    // Tablet: 2x2 larger grid
                    return GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      childAspectRatio: 2.5,
                      crossAxisSpacing: 6,
                      mainAxisSpacing: 6,
                      children: [
                        _buildEnhancedStatCard(
                          'عدد الأعواد المطلوبة',
                          '${_optimizedSticks.length}',
                          Icons.straighten,
                          Colors.blue,
                          'عود',
                          isCompact: false,
                        ),
                        _buildEnhancedStatCard(
                          'طول العود الواحد',
                          widget.stickLength.toStringAsFixed(0),
                          Icons.height,
                          Colors.green,
                          'سم',
                          isCompact: false,
                        ),
                        _buildEnhancedStatCard(
                          'إجمالي الهدر',
                          _totalWaste.toStringAsFixed(1),
                          Icons.delete_outline,
                          Colors.orange,
                          'سم',
                          isCompact: false,
                        ),
                        _buildEnhancedStatCard(
                          'نسبة الهدر',
                          _wastePercentage.toStringAsFixed(1),
                          Icons.pie_chart,
                          _wastePercentage < 10 ? Colors.green : _wastePercentage < 20 ? Colors.orange : Colors.red,
                          '%',
                          isCompact: false,
                        ),
                      ],
                    );
                  } else {
                    // Desktop: Single row
                    return Row(
                      children: [
                        Expanded(
                          child: _buildEnhancedStatCard(
                            'عدد الأعواد المطلوبة',
                            '${_optimizedSticks.length}',
                            Icons.straighten,
                            Colors.blue,
                            'عود',
                            isCompact: false,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildEnhancedStatCard(
                            'طول العود الواحد',
                            widget.stickLength.toStringAsFixed(0),
                            Icons.height,
                            Colors.green,
                            'سم',
                            isCompact: false,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildEnhancedStatCard(
                            'إجمالي الهدر',
                            _totalWaste.toStringAsFixed(1),
                            Icons.delete_outline,
                            Colors.orange,
                            'سم',
                            isCompact: false,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildEnhancedStatCard(
                            'نسبة الهدر',
                            _wastePercentage.toStringAsFixed(1),
                            Icons.pie_chart,
                            _wastePercentage < 10 ? Colors.green : _wastePercentage < 20 ? Colors.orange : Colors.red,
                            '%',
                            isCompact: false,
                          ),
                        ),
                      ],
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Enhanced stat card with better visual design
  Widget _buildEnhancedStatCard(String title, String value, IconData icon, Color color, String unit, {bool isCompact = false}) {
    return Container(
      padding: EdgeInsets.all(isCompact ? 8 : 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(isCompact ? 8 : 12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(isCompact ? 6 : 8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(isCompact ? 6 : 8),
            ),
            child: Icon(
              icon,
              color: color,
              size: isCompact ? 16 : 20,
            ),
          ),
          SizedBox(height: isCompact ? 6 : 8),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(
                  text: value,
                  style: TextStyle(
                    fontSize: isCompact ? 14 : 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                if (unit.isNotEmpty)
                  TextSpan(
                    text: ' $unit',
                    style: TextStyle(
                      fontSize: isCompact ? 10 : 12,
                      fontWeight: FontWeight.w600,
                      color: color.withValues(alpha: 0.8),
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: isCompact ? 3 : 6),
          Text(
            title,
            style: TextStyle(
              fontSize: isCompact ? 9 : 10,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: isCompact ? 2 : 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Helper method to format size with decimals when needed
  String _formatSize(double size) {
    if (size == size.roundToDouble()) {
      return size.toStringAsFixed(0);
    } else {
      return size.toStringAsFixed(1);
    }
  }





  Widget _buildOptimizationResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.content_cut,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل التقطيع',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'عرض مفصل لكل عود وقطعه',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${_optimizedSticks.length} عود',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _optimizedSticks.length,
          itemBuilder: (context, index) {
            return _buildStickCard(_optimizedSticks[index]);
          },
        ),
      ],
    );
  }

  Widget _buildStickCard(OptimizedStick stick) {
    final wastePercentage = (stick.wasteLength / widget.stickLength) * 100;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey[50]!,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Stick header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.straighten,
                          color: Theme.of(context).colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'العود رقم ${stick.stickNumber}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: wastePercentage < 10
                            ? Colors.green.withValues(alpha: 0.2)
                            : wastePercentage < 20
                                ? Colors.orange.withValues(alpha: 0.2)
                                : Colors.red.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: wastePercentage < 10
                              ? Colors.green.withValues(alpha: 0.5)
                              : wastePercentage < 20
                                  ? Colors.orange.withValues(alpha: 0.5)
                                  : Colors.red.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            wastePercentage < 10
                                ? Icons.check_circle
                                : wastePercentage < 20
                                    ? Icons.warning
                                    : Icons.error,
                            size: 16,
                            color: wastePercentage < 10
                                ? Colors.green[700]
                                : wastePercentage < 20
                                    ? Colors.orange[700]
                                    : Colors.red[700],
                          ),
                          const SizedBox(width: 4),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'هدر: ${_formatSize(stick.wasteLength)} سم',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: wastePercentage < 10
                                      ? Colors.green[700]
                                      : wastePercentage < 20
                                          ? Colors.orange[700]
                                          : Colors.red[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'نسبة: ${wastePercentage.toStringAsFixed(1)}%',
                                style: TextStyle(
                                  fontSize: 9,
                                  color: wastePercentage < 10
                                      ? Colors.green[600]
                                      : wastePercentage < 20
                                          ? Colors.orange[600]
                                          : Colors.red[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Pieces summary
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.content_cut,
                      color: Colors.blue[700],
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'عدد القطع: ${stick.pieces.length}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue[700],
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'المجموع: ${stick.pieces.fold(0.0, (sum, p) => sum + p.size).toStringAsFixed(1)} سم',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Enhanced Stick visualization
              _buildEnhancedStickVisualization(stick),

              const SizedBox(height: 12),

              // Detailed pieces list for mobile
              LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth < 600) {
                    return _buildMobilePiecesList(stick);
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedStickVisualization(OptimizedStick stick) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive height based on screen size
        double visualHeight = constraints.maxWidth < 600 ? 80 : 100;

        return Container(
          height: visualHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey[100]!,
                Colors.grey[50]!,
              ],
            ),
            border: Border.all(color: Colors.grey[300]!, width: 2),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: CustomPaint(
            painter: EnhancedStickPainter(
              stick: stick,
              stickLength: widget.stickLength,
              sawBladeThickness: widget.sawBladeThickness,
              isMobile: constraints.maxWidth < 600,
            ),
            child: Container(),
          ),
        );
      },
    );
  }

  Widget _buildMobilePiecesList(OptimizedStick stick) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.list_alt,
                color: Colors.grey[600],
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                'تفاصيل القطع',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: stick.pieces.asMap().entries.map((entry) {
              final index = entry.key;
              final piece = entry.value;

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.blue[300]!),
                ),
                child: Text(
                  '${index + 1}: ${_formatSize(piece.size)} سم',
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

}

// Enhanced custom painter for stick visualization
class EnhancedStickPainter extends CustomPainter {
  final OptimizedStick stick;
  final double stickLength;
  final double sawBladeThickness;
  final bool isMobile;

  EnhancedStickPainter({
    required this.stick,
    required this.stickLength,
    required this.sawBladeThickness,
    this.isMobile = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    final strokePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..color = Colors.grey[600]!;

    // Enhanced stick outline with gradient
    final stickRect = Rect.fromLTWH(0, 15, size.width, size.height - 30);
    final stickGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Colors.grey[300]!,
        Colors.grey[200]!,
        Colors.grey[100]!,
      ],
    );

    paint.shader = stickGradient.createShader(stickRect);
    canvas.drawRRect(
      RRect.fromRectAndRadius(stickRect, const Radius.circular(6)),
      paint,
    );

    // Draw stick border
    paint.shader = null;
    canvas.drawRRect(
      RRect.fromRectAndRadius(stickRect, const Radius.circular(6)),
      strokePaint,
    );

    // Color palette for pieces
    final List<Color> pieceColors = [
      Colors.blue[300]!,
      Colors.green[300]!,
      Colors.orange[300]!,
      Colors.purple[300]!,
      Colors.teal[300]!,
      Colors.indigo[300]!,
    ];

    // Draw pieces with enhanced visualization
    double currentX = 0;
    for (int i = 0; i < stick.pieces.length; i++) {
      final piece = stick.pieces[i];
      final pieceWidth = (piece.size / stickLength) * size.width;
      final pieceColor = pieceColors[i % pieceColors.length];

      // Draw piece with gradient and shadow
      final pieceRect = Rect.fromLTWH(
        currentX + 1,
        17,
        pieceWidth - 2,
        size.height - 34,
      );

      // Piece gradient
      final pieceGradient = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          pieceColor.withValues(alpha: 0.8),
          pieceColor.withValues(alpha: 0.6),
          pieceColor.withValues(alpha: 0.9),
        ],
      );

      paint.shader = pieceGradient.createShader(pieceRect);
      canvas.drawRRect(
        RRect.fromRectAndRadius(pieceRect, const Radius.circular(4)),
        paint,
      );

      // Piece border
      paint.shader = null;
      paint.style = PaintingStyle.stroke;
      paint.color = pieceColor.withValues(alpha: 0.8);
      paint.strokeWidth = 1.5;
      canvas.drawRRect(
        RRect.fromRectAndRadius(pieceRect, const Radius.circular(4)),
        paint,
      );
      paint.style = PaintingStyle.fill;

      // Enhanced text display based on piece width - only show size
      if (pieceWidth > (isMobile ? 15 : 20)) {
        // Size only for all pieces (removed numbers)
        _drawSizeOnly(canvas, pieceRect, piece, isMobile);
      }

      currentX += pieceWidth;

      // Enhanced saw cut visualization
      if (i < stick.pieces.length - 1) {
        final cutPaint = Paint()
          ..shader = LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.red[400]!,
              Colors.red[600]!,
              Colors.red[400]!,
            ],
          ).createShader(Rect.fromLTWH(currentX, 15, (sawBladeThickness / stickLength) * size.width, size.height - 30));

        final cutWidth = (sawBladeThickness / stickLength) * size.width;
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(currentX, 15, cutWidth, size.height - 30),
            const Radius.circular(2),
          ),
          cutPaint,
        );
        currentX += cutWidth;
      }
    }

    // Enhanced waste area visualization
    if (stick.wasteLength > 0) {
      final wasteWidth = (stick.wasteLength / stickLength) * size.width;
      final wasteRect = Rect.fromLTWH(
        currentX + 1,
        17,
        wasteWidth - 2,
        size.height - 34,
      );

      // Waste area with diagonal stripes pattern
      _drawWasteArea(canvas, wasteRect, stick.wasteLength, isMobile);
    }

    // Enhanced measurement display
    _drawMeasurements(canvas, size, isMobile);
  }



  void _drawSizeOnly(Canvas canvas, Rect rect, CuttingPiece piece, bool isMobile) {
    final sizePainter = TextPainter(
      text: TextSpan(
        text: _formatSize(piece.size),
        style: TextStyle(
          color: Colors.black87,
          fontSize: isMobile ? 14 : 16, // تكبير الخط من 9-10 إلى 14-16
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    sizePainter.layout();

    sizePainter.paint(
      canvas,
      Offset(
        rect.left + (rect.width - sizePainter.width) / 2,
        rect.top + (rect.height - sizePainter.height) / 2,
      ),
    );
  }



  void _drawWasteArea(Canvas canvas, Rect rect, double wasteLength, bool isMobile) {
    // Base waste color
    final wastePaint = Paint()
      ..color = Colors.pink[100]!.withValues(alpha: 0.7);
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      wastePaint,
    );

    // Create a clipping path to ensure diagonal stripes stay within the waste area only
    canvas.save();
    canvas.clipRRect(RRect.fromRectAndRadius(rect, const Radius.circular(4)));

    // Diagonal stripes for waste area ONLY
    final stripePaint = Paint()
      ..color = Colors.pink[300]!.withValues(alpha: 0.9)
      ..strokeWidth = 1.5;

    // Draw diagonal stripes only within the waste rectangle bounds
    for (double i = rect.left - rect.height; i < rect.right + rect.height; i += 6) {
      final startX = i;
      final endX = i + rect.height;

      // Only draw lines that intersect with the waste rectangle
      if (endX > rect.left && startX < rect.right) {
        canvas.drawLine(
          Offset(startX, rect.top),
          Offset(endX, rect.bottom),
          stripePaint,
        );
      }
    }

    // Restore the clipping state
    canvas.restore();

    // Waste border
    final borderPaint = Paint()
      ..color = Colors.pink[400]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      borderPaint,
    );

    // Waste text
    if (rect.width > (isMobile ? 20 : 30)) {
      final wastePercentage = (wasteLength / stickLength) * 100;
      final wasteText = rect.width > (isMobile ? 40 : 60)
          ? 'هدر\n${_formatSize(wasteLength)}\n${wastePercentage.toStringAsFixed(1)}%'
          : '${wastePercentage.toStringAsFixed(1)}%';

      final wastePainter = TextPainter(
        text: TextSpan(
          text: wasteText,
          style: TextStyle(
            color: Colors.pink[700],
            fontSize: isMobile ? 7 : 8,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.rtl,
        textAlign: TextAlign.center,
      );
      wastePainter.layout();

      wastePainter.paint(
        canvas,
        Offset(
          rect.left + (rect.width - wastePainter.width) / 2,
          rect.top + (rect.height - wastePainter.height) / 2,
        ),
      );
    }
  }

  void _drawMeasurements(Canvas canvas, Size size, bool isMobile) {
    final measurementPaint = Paint()
      ..color = Colors.blue[700]!
      ..strokeWidth = 2;

    // Top measurement line
    canvas.drawLine(
      const Offset(0, 8),
      Offset(size.width, 8),
      measurementPaint,
    );

    // Bottom measurement line
    canvas.drawLine(
      Offset(0, size.height - 8),
      Offset(size.width, size.height - 8),
      measurementPaint,
    );

    // Measurement text
    final measurementText = TextPainter(
      text: TextSpan(
        text: 'طول العود: ${_formatSize(stickLength)} سم',
        style: TextStyle(
          color: Colors.blue[800],
          fontSize: isMobile ? 10 : 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    measurementText.layout();

    // Background for measurement text
    final textBgPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.95);
    final textBgRect = Rect.fromLTWH(
      (size.width - measurementText.width) / 2 - 6,
      1,
      measurementText.width + 12,
      measurementText.height + 4,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(textBgRect, const Radius.circular(4)),
      textBgPaint,
    );

    measurementText.paint(
      canvas,
      Offset((size.width - measurementText.width) / 2, 3),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  String _formatSize(double size) {
    if (size == size.roundToDouble()) {
      return size.toStringAsFixed(0);
    } else {
      return size.toStringAsFixed(1);
    }
  }
}

// Old stick painter - kept for compatibility but not used
class StickPainter extends CustomPainter {
  final OptimizedStick stick;
  final double stickLength;
  final double sawBladeThickness;

  StickPainter({
    required this.stick,
    required this.stickLength,
    required this.sawBladeThickness,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    final strokePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..color = Colors.grey[400]!;

    // Draw stick outline
    final stickRect = Rect.fromLTWH(0, 10, size.width, size.height - 20);
    canvas.drawRRect(
      RRect.fromRectAndRadius(stickRect, const Radius.circular(4)),
      strokePaint,
    );

    // Draw pieces
    double currentX = 0;
    for (int i = 0; i < stick.pieces.length; i++) {
      final piece = stick.pieces[i];
      final pieceWidth = (piece.size / stickLength) * size.width;

      // Draw piece with light grey color
      paint.color = Colors.grey[50]!; // Light grey color for all pieces
      final pieceRect = Rect.fromLTWH(
        currentX,
        12,
        pieceWidth,
        size.height - 24,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(pieceRect, const Radius.circular(2)),
        paint,
      );

      // Draw piece information (number and size)
      if (pieceWidth > 30) {
        // Draw piece number
        final numberTextPainter = TextPainter(
          text: TextSpan(
            text: '${i + 1}',
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        numberTextPainter.layout();

        // Draw piece size with larger font
        final sizeTextPainter = TextPainter(
          text: TextSpan(
            text: _formatSize(piece.size),
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.rtl,
        );
        sizeTextPainter.layout();

        // Calculate total text height
        final totalTextHeight = numberTextPainter.height + sizeTextPainter.height + 2;
        final startY = 12 + (size.height - 24 - totalTextHeight) / 2;

        // Draw number
        numberTextPainter.paint(
          canvas,
          Offset(
            currentX + (pieceWidth - numberTextPainter.width) / 2,
            startY,
          ),
        );

        // Draw size below number
        sizeTextPainter.paint(
          canvas,
          Offset(
            currentX + (pieceWidth - sizeTextPainter.width) / 2,
            startY + numberTextPainter.height + 2,
          ),
        );
      } else if (pieceWidth > 15) {
        // For smaller pieces, show only the size with larger font
        final sizeTextPainter = TextPainter(
          text: TextSpan(
            text: _formatSize(piece.size),
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.rtl,
        );
        sizeTextPainter.layout();

        sizeTextPainter.paint(
          canvas,
          Offset(
            currentX + (pieceWidth - sizeTextPainter.width) / 2,
            12 + (size.height - 24 - sizeTextPainter.height) / 2,
          ),
        );
      }

      currentX += pieceWidth;

      // Draw saw cut line (except for last piece)
      if (i < stick.pieces.length - 1) {
        final cutPaint = Paint()
          ..color = Colors.red[300]!
          ..strokeWidth = 2;

        final cutWidth = (sawBladeThickness / stickLength) * size.width;
        canvas.drawRect(
          Rect.fromLTWH(currentX, 10, cutWidth, size.height - 20),
          cutPaint,
        );
        currentX += cutWidth;
      }
    }

    // Draw waste area
    if (stick.wasteLength > 0) {
      final wasteWidth = (stick.wasteLength / stickLength) * size.width;
      paint.color = Colors.pink[100]!.withValues(alpha: 0.6); // Light transparent pink for waste area
      final wasteRect = Rect.fromLTWH(
        currentX,
        12,
        wasteWidth,
        size.height - 24,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(wasteRect, const Radius.circular(2)),
        paint,
      );

      // Draw waste text with percentage
      if (wasteWidth > 30) {
        final wastePercentage = (stick.wasteLength / stickLength) * 100;
        final wasteTextPainter = TextPainter(
          text: TextSpan(
            text: 'هدر\n${_formatSize(stick.wasteLength)}\n(${wastePercentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 9,
              fontWeight: FontWeight.w500,
            ),
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        );
        wasteTextPainter.layout();
        wasteTextPainter.paint(
          canvas,
          Offset(
            currentX + (wasteWidth - wasteTextPainter.width) / 2,
            12 + (size.height - 24 - wasteTextPainter.height) / 2,
          ),
        );
      } else if (wasteWidth > 15) {
        // For smaller waste areas, show only percentage
        final wastePercentage = (stick.wasteLength / stickLength) * 100;
        final wasteTextPainter = TextPainter(
          text: TextSpan(
            text: '${wastePercentage.toStringAsFixed(1)}%',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        );
        wasteTextPainter.layout();
        wasteTextPainter.paint(
          canvas,
          Offset(
            currentX + (wasteWidth - wasteTextPainter.width) / 2,
            12 + (size.height - 24 - wasteTextPainter.height) / 2,
          ),
        );
      }
    }

    // Draw measurements
    final measurementPaint = Paint()
      ..color = Colors.blue[700]!
      ..strokeWidth = 2;

    // Top measurement line
    canvas.drawLine(
      const Offset(0, 5),
      Offset(size.width, 5),
      measurementPaint,
    );

    // Measurement text with enhanced visibility
    final measurementTextPainter = TextPainter(
      text: TextSpan(
        text: 'طول العود: ${_formatSize(stickLength)}',
        style: TextStyle(
          color: Colors.blue[800],
          fontSize: 12,
          fontWeight: FontWeight.bold,
          backgroundColor: Colors.white.withValues(alpha: 0.9),
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    measurementTextPainter.layout();

    // Draw background for text
    final textBackground = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;

    final textRect = Rect.fromLTWH(
      (size.width - measurementTextPainter.width) / 2 - 4,
      -2,
      measurementTextPainter.width + 8,
      measurementTextPainter.height + 4,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(textRect, const Radius.circular(4)),
      textBackground,
    );

    measurementTextPainter.paint(
      canvas,
      Offset(
        (size.width - measurementTextPainter.width) / 2,
        0,
      ),
    );
  }



  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  // Helper method to format size with decimals when needed
  String _formatSize(double size) {
    if (size == size.roundToDouble()) {
      return size.toStringAsFixed(0);
    } else {
      return size.toStringAsFixed(1);
    }
  }
}

// Data models
class OptimizedStick {
  final int stickNumber;
  final double totalLength;
  final double sawBladeThickness;
  final List<CuttingPiece> pieces;
  double wasteLength;

  OptimizedStick({
    required this.stickNumber,
    required this.totalLength,
    required this.sawBladeThickness,
    List<CuttingPiece>? pieces,
    this.wasteLength = 0.0,
  }) : pieces = pieces ?? <CuttingPiece>[];
}

class CuttingPiece {
  final double size;
  final String type;
  final String number;
  final String originalSpec;
  final double position;

  CuttingPiece({
    required this.size,
    required this.type,
    required this.number,
    required this.originalSpec,
    this.position = 0.0,
  });

  CuttingPiece copyWith({
    double? size,
    String? type,
    String? number,
    String? originalSpec,
    double? position,
  }) {
    return CuttingPiece(
      size: size ?? this.size,
      type: type ?? this.type,
      number: number ?? this.number,
      originalSpec: originalSpec ?? this.originalSpec,
      position: position ?? this.position,
    );
  }
}